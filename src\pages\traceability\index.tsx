import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button } from 'antd';
import traceApi from '@/service/traceApi';
import type { TraceRecord, ApiResponse } from '@/service/traceApi';
import locales from '@/locales';
import TraceabilityModal from './components/TraceabilityModal';
import formApi from '@/service/formApi';

type filterType = {
  highRiskEnterpriseName?: string;
  lowRiskEnterpriseName?: string;
  pollutionType?: string;
  tracingCode?: string;
  tracingTime?: string;
  alarmId?: string;
};

type filterTypeData = {
  highRiskEnterpriseName?: string;
  lowRiskEnterpriseName?: string;
  pollutionType?: string;
  tracingCode?: string;
  tracingTime?: string;
  alarmId?: string;
};

/**
 * @description 溯源管理页面
 * @returns
 */
const TraceabilityList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType] = useState<'view'>('view'); // 只有查看功能
  const [dataObj, setDataObj] = useState<TraceRecord>();
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  /**
   * 处理筛选条件
   * @param filter 筛选条件
   * @returns 处理后的筛选条件
   */
  const handleFilter = (filter: filterType): filterTypeData => {
    const result: filterTypeData = {};

    if (filter.highRiskEnterpriseName) {
      result.highRiskEnterpriseName = filter.highRiskEnterpriseName;
    }
    if (filter.lowRiskEnterpriseName) {
      result.lowRiskEnterpriseName = filter.lowRiskEnterpriseName;
    }
    if (filter.pollutionType) {
      result.pollutionType = filter.pollutionType;
    }
    if (filter.tracingCode) {
      result.tracingCode = filter.tracingCode;
    }
    if (filter.tracingTime) {
      result.tracingTime = filter.tracingTime;
    }
    if (filter.alarmId) {
      result.alarmId = filter.alarmId;
    }

    return result;
  };

  /**
   * 关闭弹窗
   */
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    setDataObj({});
    // 刷新列表
    if (ref.current) {
      ref.current.reload({});
    }
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },

    {
      dataIndex: 'pollutionType',
      title: '污染类型',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A08A06',
            },
            currentPage: 0,
            pageSize: 0,
          });
          return list;
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (value: string, record: TraceRecord) => {
        return record.pollutionTypeText || '-';
      },
    },
    {
      dataIndex: 'tracingCode',
      title: '溯源指标',
      width: 120,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'pollutantConcentration',
      title: '相关报警值',
      width: 120,
      render: (value: number) => {
        return value ? value.toFixed(2) : '-';
      },
    },
    {
      dataIndex: 'highRiskEnterpriseName',
      title: '高风险企业',
      width: 180,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入高风险企业名称',
      },
      ellipsis: true,
      render: (value: string) => {
        return value ? (
          <span
            style={{
              backgroundColor: 'rgba(255, 0, 0, 0.1)', // 红色背景，透明度0.1
              padding: '4px 8px', // 内边距
            }}
          >
            {value}
          </span>
        ) : (
          '-'
        );
      },
    },
    {
      dataIndex: 'lowRiskEnterpriseName',
      title: '低风险企业',
      width: 180,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入低风险企业名称',
      },
      ellipsis: true,
    },
    {
      dataIndex: 'tracingTime',
      title: '溯源时间',
      width: 160,
      query: true,
      display: true,
      componentName: 'DatePicker',
      componentProps: {
        precision: 'second',
        formatter: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择溯源时间',
      },
    },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="traceabilityList"
        action={aa}
        searchMemory
        actionRef={ref}
        showRowSelection={false}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ApiResponse<TraceRecord[]> = await traceApi.queryTraceRecordList({
            aescs: [],
            descs: [],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={70}
        rowOperation={(row) => {
          return [
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setDataObj(row);
                    setEditMenuVisiable(true);
                  }}
                >
                  查看
                </Button>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title="溯源记录详情"
        width="70%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={editMenuVisiable}
        key="traceability-modal"
      >
        <TraceabilityModal modalType={modalType} closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(TraceabilityList, locales, YTHLocalization.getLanguage());
